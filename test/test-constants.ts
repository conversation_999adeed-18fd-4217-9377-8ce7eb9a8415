/**
 * Test Constants
 *
 * This file contains constants used across test files to avoid hardcoded credentials
 * and ensure consistency in test configuration.
 */

/**
 * Test credentials for API authentication
 * Uses environment variables to match the actual application configuration
 */
export const TEST_CREDENTIALS = {
  USERNAME: process.env.AUTH_USERNAME || 'tms_dev_api_user_2025',
  PASSWORD: process.env.AUTH_PASSWORD || 'TmS_D3v_4P1_P4ssw0rd_2025_S3cur3',
  get BASIC_AUTH_HEADER() {
    return Buffer.from(`${this.USERNAME}:${this.PASSWORD}`).toString('base64');
  },
} as const;

/**
 * Test database configuration
 * Uses environment variables with test-specific fallbacks
 */
export const TEST_DB_CONFIG = {
  USERNAME: process.env.DB_USERNAME || 'test_db_user_2025',
  PASSWORD: process.env.DB_PASSWORD || 'T3st_DB_P4ssw0rd_2025_S3cur3',
  DATABASE: process.env.DB_DATABASE || 'tms-dev-postgres-database',
  HOST: process.env.DB_HOST || 'localhost',
  PORT: parseInt(process.env.DB_PORT || '5432', 10),
} as const;

/**
 * Test MinIO configuration
 * Uses environment variables with test-specific fallbacks
 */
export const TEST_MINIO_CONFIG = {
  ACCESS_KEY: process.env.MINIO_ACCESS_KEY || 'test_minio_access_2025',
  SECRET_KEY: process.env.MINIO_SECRET_KEY || 'T3st_M1n10_S3cr3t_2025_S3cur3',
  BUCKET: process.env.MINIO_DEFAULT_BUCKET || 'tms-dev-minio-bucket',
  ENDPOINT: process.env.MINIO_ENDPOINT || 'localhost',
  PORT: parseInt(process.env.MINIO_PORT || '9000', 10),
} as const;

/**
 * Common test correlation ID for consistent testing
 */
export const TEST_CORRELATION_ID = '123e4567-e89b-12d3-a456-426614174000';

/**
 * Test file paths
 */
export const TEST_PATHS = {
  TEST_DATA_DIR: '../../test-data/quiz-zip-files',
  VALID_ZIP_FILE: 'Dr Du_Math__V2_Y09_Trigonometry_A1.R_for_Y09__F2F QZ.zip',
} as const;
